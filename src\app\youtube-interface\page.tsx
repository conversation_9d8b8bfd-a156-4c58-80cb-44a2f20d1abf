'use client';

import { useState } from 'react';
import YouTubeHeader from '@/components/youtube/YouTubeHeader';
import YouTubeSidebar from '@/components/youtube/YouTubeSidebar';
import YouTubeContent from '@/components/youtube/YouTubeContent';

export default function YouTubeInterface() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className="h-screen bg-[#0f0f0f] text-white overflow-hidden">
      {/* Fixed Header - Full width at top */}
      <YouTubeHeader onToggleSidebar={toggleSidebar} />

      {/* Main Layout - Starts below header, takes remaining height */}
      <div className="flex h-[calc(100vh-56px)] mt-14">
        {/* Sidebar - Full height from below header to bottom */}
        <YouTubeSidebar isCollapsed={isSidebarCollapsed} />

        {/* Content Area - Full height from below header to bottom */}
        <YouTubeContent isCollapsed={isSidebarCollapsed} />
      </div>
    </div>
  );
}
