'use client';

import { useRef, useEffect, useLayoutEffect } from 'react';
import { 
  HomeIcon,
  PlayIcon,
  ClockIcon,
  HandThumbUpIcon,
  FolderIcon,
  ChevronDownIcon,
  FireIcon,
  MusicalNoteIcon,
  FilmIcon,
  TvIcon,
  NewspaperIcon,
  TrophyIcon,
  AcademicCapIcon,
  CogIcon,
  FlagIcon,
  QuestionMarkCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface YouTubeSidebarProps {
  isCollapsed: boolean;
}

export default function YouTubeSidebar({ isCollapsed }: YouTubeSidebarProps) {
  const expandedScrollRef = useRef<HTMLDivElement>(null);
  const collapsedScrollRef = useRef<HTMLDivElement>(null);
  const savedExpandedScrollPosition = useRef<number>(0);

  // 🎯 FIXED: Separate effects for capture and restore with correct timing
  useLayoutEffect(() => {
    if (!isCollapsed) {
      const expandedContainer = expandedScrollRef.current;

      // RESTORE: Apply saved scroll position immediately when expanding
      if (expandedContainer && savedExpandedScrollPosition.current > 0) {
        // Validate against actual scrollable content
        const maxScroll = expandedContainer.scrollHeight - expandedContainer.clientHeight;
        const targetScroll = Math.min(savedExpandedScrollPosition.current, maxScroll);

        expandedContainer.scrollTop = targetScroll;
        console.log('🎯 PERFECT RESTORE - Position:', targetScroll, 'Max:', maxScroll);
      }
    }
  }, [isCollapsed]);

  // 🎯 CAPTURE: Save scroll position when about to collapse
  useLayoutEffect(() => {
    if (isCollapsed) {
      // We just collapsed - the expanded container was just unmounted
      // But we should have captured the position in the previous render cycle
      console.log('🎯 COLLAPSED - Using saved position:', savedExpandedScrollPosition.current);
    } else {
      // We're expanded - set up cleanup to capture position when collapsing
      return () => {
        const expandedContainer = expandedScrollRef.current;
        if (expandedContainer) {
          savedExpandedScrollPosition.current = expandedContainer.scrollTop;
          console.log('🎯 PERFECT SAVE - Captured position:', savedExpandedScrollPosition.current);
        }
      };
    }
  }, [isCollapsed]);

  // 💾 Continuous scroll position tracking when expanded
  useEffect(() => {
    if (!isCollapsed) {
      const expandedContainer = expandedScrollRef.current;
      if (!expandedContainer) return;

      const handleScroll = () => {
        savedExpandedScrollPosition.current = expandedContainer.scrollTop;
      };

      expandedContainer.addEventListener('scroll', handleScroll, { passive: true });
      return () => expandedContainer.removeEventListener('scroll', handleScroll);
    }
  }, [isCollapsed]);

  const mainMenuItems = [
    { icon: HomeIcon, label: 'Home', active: true },
    { icon: PlayIcon, label: 'Shorts' },
    { icon: FolderIcon, label: 'Subscriptions' },
  ];

  const youMenuItems = [
    { icon: ClockIcon, label: 'History' },
    { icon: PlayIcon, label: 'Your videos' },
    { icon: ClockIcon, label: 'Watch later' },
    { icon: HandThumbUpIcon, label: 'Liked videos' },
  ];

  const exploreItems = [
    { icon: FireIcon, label: 'Trending' },
    { icon: MusicalNoteIcon, label: 'Music' },
    { icon: FilmIcon, label: 'Movies' },
    { icon: TvIcon, label: 'Live' },
    { icon: NewspaperIcon, label: 'News' },
    { icon: TrophyIcon, label: 'Sports' },
    { icon: AcademicCapIcon, label: 'Learning' },
  ];

  const subscriptions = [
    { name: 'Tech Channel', avatar: '🔧' },
    { name: 'Music World', avatar: '🎵' },
    { name: 'Gaming Hub', avatar: '🎮' },
    { name: 'Cooking Master', avatar: '👨‍🍳' },
    { name: 'Travel Vlog', avatar: '✈️' },
    { name: 'Science Today', avatar: '🔬' },
    { name: 'Art Studio', avatar: '🎨' },
    { name: 'Fitness Pro', avatar: '💪' },
  ];

  const settingsItems = [
    { icon: CogIcon, label: 'Settings' },
    { icon: FlagIcon, label: 'Report history' },
    { icon: QuestionMarkCircleIcon, label: 'Help' },
    { icon: ExclamationTriangleIcon, label: 'Send feedback' },
  ];

  return (
    <aside className={`bg-[#0f0f0f] border-r border-[#272727] flex-shrink-0 transition-all duration-300 ${
      isCollapsed ? 'w-[72px]' : 'w-[240px]'
    }`}>
      {/* COLLAPSED SIDEBAR - Independent scroll container */}
      {isCollapsed && (
        <div
          ref={collapsedScrollRef}
          className="h-full overflow-y-auto scrollbar-youtube"
        >
          <div className="pt-4">
            {/* Only main menu items in collapsed mode */}
            <div className="px-1">
              {mainMenuItems.map((item, index) => (
                <div
                  key={index}
                  className={`flex flex-col items-center py-4 px-1 hover:bg-[#272727] cursor-pointer transition-colors ${
                    item.active ? 'bg-[#272727]' : ''
                  }`}
                >
                  <item.icon className="w-6 h-6 mb-1" />
                  <span className="text-xs text-center">{item.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* EXPANDED SIDEBAR - Independent scroll container */}
      {!isCollapsed && (
        <div
          ref={expandedScrollRef}
          className="h-full overflow-y-auto scrollbar-youtube"
        >
          <div className="pt-4">
            {/* Main Menu */}
            <div className="px-3">
              {mainMenuItems.map((item, index) => (
                <div
                  key={index}
                  className={`flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors ${
                    item.active ? 'bg-[#272727]' : ''
                  }`}
                >
                  <item.icon className="w-6 h-6" />
                  <span className="text-sm">{item.label}</span>
                </div>
              ))}
            </div>

            <hr className="border-[#272727] my-3" />

            {/* You Section */}
            <div className="px-3">
              <div className="flex items-center space-x-6 px-3 py-2">
                <span className="text-sm font-medium">You</span>
                <ChevronDownIcon className="w-4 h-4" />
              </div>
              {youMenuItems.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                >
                  <item.icon className="w-6 h-6" />
                  <span className="text-sm">{item.label}</span>
                </div>
              ))}
            </div>

            <hr className="border-[#272727] my-3" />

            {/* Subscriptions */}
            <div className="px-3">
              <div className="flex items-center space-x-6 px-3 py-2">
                <span className="text-sm font-medium">Subscriptions</span>
              </div>
              {subscriptions.map((sub, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                >
                  <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-sm">
                    {sub.avatar}
                  </div>
                  <span className="text-sm">{sub.name}</span>
                </div>
              ))}
            </div>

            <hr className="border-[#272727] my-3" />

            {/* Explore */}
            <div className="px-3">
              <div className="px-3 py-2">
                <span className="text-sm font-medium">Explore</span>
              </div>
              {exploreItems.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                >
                  <item.icon className="w-6 h-6" />
                  <span className="text-sm">{item.label}</span>
                </div>
              ))}
            </div>

            <hr className="border-[#272727] my-3" />

            {/* Settings */}
            <div className="px-3 pb-4">
              {settingsItems.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-6 px-3 py-2 rounded-lg hover:bg-[#272727] cursor-pointer transition-colors"
                >
                  <item.icon className="w-6 h-6" />
                  <span className="text-sm">{item.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </aside>
  );
}
