'use client';

import { useState } from 'react';
import YouTubeHeader from '@/components/youtube/YouTubeHeader';
import YouTubeSidebar from '@/components/youtube/YouTubeSidebar';
import YouTubeContent from '@/components/youtube/YouTubeContent';

export default function YouTubeInterface() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className="h-screen bg-[#0f0f0f] text-white overflow-hidden flex flex-col">
      {/* Header */}
      <YouTubeHeader onToggleSidebar={toggleSidebar} />

      {/* Main Layout - Takes remaining height */}
      <div className="flex flex-1 min-h-0">
        {/* Sidebar */}
        <YouTubeSidebar isCollapsed={isSidebarCollapsed} />

        {/* Content Area */}
        <YouTubeContent isCollapsed={isSidebarCollapsed} />
      </div>
    </div>
  );
}
